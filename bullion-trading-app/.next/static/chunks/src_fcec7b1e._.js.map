{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/Adittya/Bullion%20app/main/bullion-trading-app/src/lib/supabase/client.ts"], "sourcesContent": ["import { createBrowserClient } from '@supabase/ssr'\nimport { createClient } from '@supabase/supabase-js'\n\nconst supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!\nconst supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!\n\n// Client-side Supabase client\nexport const supabase = createBrowserClient(\n  supabaseUrl,\n  supabaseAnonKey\n)\n\n// Server-side Supabase client with service role (for admin operations)\nexport const supabaseAdmin = createClient(\n  supabaseUrl,\n  process.env.SUPABASE_SERVICE_ROLE_KEY!,\n  {\n    auth: {\n      autoRefreshToken: false,\n      persistSession: false\n    }\n  }\n)\n\nexport default supabase\n"], "names": [], "mappings": ";;;;;AAGoB;AAHpB;AAAA;AACA;;;AAEA,MAAM;AACN,MAAM;AAGC,MAAM,WAAW,CAAA,GAAA,6KAAA,CAAA,sBAAmB,AAAD,EACxC,aACA;AAIK,MAAM,gBAAgB,CAAA,GAAA,0LAAA,CAAA,eAAY,AAAD,EACtC,aACA,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,yBAAyB,EACrC;IACE,MAAM;QACJ,kBAAkB;QAClB,gBAAgB;IAClB;AACF;uCAGa", "debugId": null}}, {"offset": {"line": 37, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/Adittya/Bullion%20app/main/bullion-trading-app/src/contexts/AuthContext.tsx"], "sourcesContent": ["'use client'\n\nimport React, { createContext, useContext, useEffect, useState } from 'react'\nimport { User as SupabaseUser } from '@supabase/supabase-js'\nimport { supabase } from '@/lib/supabase/client'\nimport { User, AuthState } from '@/types'\n\ninterface AuthContextType extends AuthState {\n  signUp: (email: string, password: string, fullName: string, phone?: string) => Promise<void>\n  signIn: (email: string, password: string) => Promise<void>\n  signOut: () => Promise<void>\n  sendOTP: (phone: string, type: 'registration' | 'login' | 'password_reset') => Promise<void>\n  verifyOTP: (phone: string, otp: string, type: 'registration' | 'login' | 'password_reset') => Promise<void>\n  resetPassword: (email: string) => Promise<void>\n  updateProfile: (updates: Partial<User>) => Promise<void>\n  refreshUser: () => Promise<void>\n}\n\nconst AuthContext = createContext<AuthContextType | undefined>(undefined)\n\nexport function AuthProvider({ children }: { children: React.ReactNode }) {\n  const [user, setUser] = useState<User | null>(null)\n  const [loading, setLoading] = useState(true)\n  const [error, setError] = useState<string | null>(null)\n\n  useEffect(() => {\n    // Get initial session\n    const getInitialSession = async () => {\n      try {\n        const { data: { session } } = await supabase.auth.getSession()\n        if (session?.user) {\n          await fetchUserProfile(session.user)\n        }\n      } catch (err) {\n        console.error('Error getting initial session:', err)\n        setError('Failed to load user session')\n      } finally {\n        setLoading(false)\n      }\n    }\n\n    getInitialSession()\n\n    // Listen for auth changes\n    const { data: { subscription } } = supabase.auth.onAuthStateChange(\n      async (event, session) => {\n        if (event === 'SIGNED_IN' && session?.user) {\n          await fetchUserProfile(session.user)\n        } else if (event === 'SIGNED_OUT') {\n          setUser(null)\n        }\n        setLoading(false)\n      }\n    )\n\n    return () => subscription.unsubscribe()\n  }, [])\n\n  const fetchUserProfile = async (supabaseUser: SupabaseUser) => {\n    try {\n      const { data, error } = await supabase\n        .from('users')\n        .select('*')\n        .eq('id', supabaseUser.id)\n        .single()\n\n      if (error) throw error\n\n      setUser(data)\n      setError(null)\n    } catch (err) {\n      console.error('Error fetching user profile:', err)\n      setError('Failed to load user profile')\n    }\n  }\n\n  const signUp = async (email: string, password: string, fullName: string, phone?: string) => {\n    try {\n      setLoading(true)\n      setError(null)\n\n      const { data, error } = await supabase.auth.signUp({\n        email,\n        password,\n        options: {\n          data: {\n            full_name: fullName,\n            phone: phone || null,\n          },\n        },\n      })\n\n      if (error) throw error\n\n      // If phone is provided, send OTP for verification\n      if (phone && data.user) {\n        await sendOTP(phone, 'registration')\n      }\n    } catch (err: any) {\n      setError(err.message || 'Failed to create account')\n      throw err\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const signIn = async (email: string, password: string) => {\n    try {\n      setLoading(true)\n      setError(null)\n\n      const { error } = await supabase.auth.signInWithPassword({\n        email,\n        password,\n      })\n\n      if (error) throw error\n    } catch (err: any) {\n      setError(err.message || 'Failed to sign in')\n      throw err\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const signOut = async () => {\n    try {\n      setLoading(true)\n      const { error } = await supabase.auth.signOut()\n      if (error) throw error\n    } catch (err: any) {\n      setError(err.message || 'Failed to sign out')\n      throw err\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const sendOTP = async (phone: string, type: 'registration' | 'login' | 'password_reset') => {\n    try {\n      setError(null)\n      \n      const response = await fetch('/api/auth/send-otp', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({ phone, type }),\n      })\n\n      const data = await response.json()\n\n      if (!response.ok) {\n        throw new Error(data.error || 'Failed to send OTP')\n      }\n    } catch (err: any) {\n      setError(err.message || 'Failed to send OTP')\n      throw err\n    }\n  }\n\n  const verifyOTP = async (phone: string, otp: string, type: 'registration' | 'login' | 'password_reset') => {\n    try {\n      setError(null)\n      \n      const response = await fetch('/api/auth/verify-otp', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({ phone, otp, type }),\n      })\n\n      const data = await response.json()\n\n      if (!response.ok) {\n        throw new Error(data.error || 'Failed to verify OTP')\n      }\n\n      // Refresh user profile if verification was successful\n      if (user) {\n        await refreshUser()\n      }\n    } catch (err: any) {\n      setError(err.message || 'Failed to verify OTP')\n      throw err\n    }\n  }\n\n  const resetPassword = async (email: string) => {\n    try {\n      setError(null)\n      \n      const { error } = await supabase.auth.resetPasswordForEmail(email, {\n        redirectTo: `${window.location.origin}/auth/reset-password`,\n      })\n\n      if (error) throw error\n    } catch (err: any) {\n      setError(err.message || 'Failed to send reset email')\n      throw err\n    }\n  }\n\n  const updateProfile = async (updates: Partial<User>) => {\n    try {\n      setError(null)\n      \n      if (!user) throw new Error('No user logged in')\n\n      const { error } = await supabase\n        .from('users')\n        .update(updates)\n        .eq('id', user.id)\n\n      if (error) throw error\n\n      // Update local user state\n      setUser({ ...user, ...updates })\n    } catch (err: any) {\n      setError(err.message || 'Failed to update profile')\n      throw err\n    }\n  }\n\n  const refreshUser = async () => {\n    try {\n      const { data: { user: supabaseUser } } = await supabase.auth.getUser()\n      if (supabaseUser) {\n        await fetchUserProfile(supabaseUser)\n      }\n    } catch (err) {\n      console.error('Error refreshing user:', err)\n    }\n  }\n\n  const value: AuthContextType = {\n    user,\n    loading,\n    error,\n    signUp,\n    signIn,\n    signOut,\n    sendOTP,\n    verifyOTP,\n    resetPassword,\n    updateProfile,\n    refreshUser,\n  }\n\n  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>\n}\n\nexport function useAuth() {\n  const context = useContext(AuthContext)\n  if (context === undefined) {\n    throw new Error('useAuth must be used within an AuthProvider')\n  }\n  return context\n}\n"], "names": [], "mappings": ";;;;;AAEA;AAEA;;;AAJA;;;AAkBA,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,gBAAa,AAAD,EAA+B;AAExD,SAAS,aAAa,EAAE,QAAQ,EAAiC;;IACtE,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IAC9C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,sBAAsB;YACtB,MAAM;4DAAoB;oBACxB,IAAI;wBACF,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,EAAE,GAAG,MAAM,mIAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,UAAU;wBAC5D,IAAI,SAAS,MAAM;4BACjB,MAAM,iBAAiB,QAAQ,IAAI;wBACrC;oBACF,EAAE,OAAO,KAAK;wBACZ,QAAQ,KAAK,CAAC,kCAAkC;wBAChD,SAAS;oBACX,SAAU;wBACR,WAAW;oBACb;gBACF;;YAEA;YAEA,0BAA0B;YAC1B,MAAM,EAAE,MAAM,EAAE,YAAY,EAAE,EAAE,GAAG,mIAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,iBAAiB;0CAChE,OAAO,OAAO;oBACZ,IAAI,UAAU,eAAe,SAAS,MAAM;wBAC1C,MAAM,iBAAiB,QAAQ,IAAI;oBACrC,OAAO,IAAI,UAAU,cAAc;wBACjC,QAAQ;oBACV;oBACA,WAAW;gBACb;;YAGF;0CAAO,IAAM,aAAa,WAAW;;QACvC;iCAAG,EAAE;IAEL,MAAM,mBAAmB,OAAO;QAC9B,IAAI;YACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,mIAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,SACL,MAAM,CAAC,KACP,EAAE,CAAC,MAAM,aAAa,EAAE,EACxB,MAAM;YAET,IAAI,OAAO,MAAM;YAEjB,QAAQ;YACR,SAAS;QACX,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,gCAAgC;YAC9C,SAAS;QACX;IACF;IAEA,MAAM,SAAS,OAAO,OAAe,UAAkB,UAAkB;QACvE,IAAI;YACF,WAAW;YACX,SAAS;YAET,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,mIAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,MAAM,CAAC;gBACjD;gBACA;gBACA,SAAS;oBACP,MAAM;wBACJ,WAAW;wBACX,OAAO,SAAS;oBAClB;gBACF;YACF;YAEA,IAAI,OAAO,MAAM;YAEjB,kDAAkD;YAClD,IAAI,SAAS,KAAK,IAAI,EAAE;gBACtB,MAAM,QAAQ,OAAO;YACvB;QACF,EAAE,OAAO,KAAU;YACjB,SAAS,IAAI,OAAO,IAAI;YACxB,MAAM;QACR,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,SAAS,OAAO,OAAe;QACnC,IAAI;YACF,WAAW;YACX,SAAS;YAET,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,mIAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,kBAAkB,CAAC;gBACvD;gBACA;YACF;YAEA,IAAI,OAAO,MAAM;QACnB,EAAE,OAAO,KAAU;YACjB,SAAS,IAAI,OAAO,IAAI;YACxB,MAAM;QACR,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,UAAU;QACd,IAAI;YACF,WAAW;YACX,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,mIAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,OAAO;YAC7C,IAAI,OAAO,MAAM;QACnB,EAAE,OAAO,KAAU;YACjB,SAAS,IAAI,OAAO,IAAI;YACxB,MAAM;QACR,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,UAAU,OAAO,OAAe;QACpC,IAAI;YACF,SAAS;YAET,MAAM,WAAW,MAAM,MAAM,sBAAsB;gBACjD,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBAAE;oBAAO;gBAAK;YACrC;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,KAAK,KAAK,IAAI;YAChC;QACF,EAAE,OAAO,KAAU;YACjB,SAAS,IAAI,OAAO,IAAI;YACxB,MAAM;QACR;IACF;IAEA,MAAM,YAAY,OAAO,OAAe,KAAa;QACnD,IAAI;YACF,SAAS;YAET,MAAM,WAAW,MAAM,MAAM,wBAAwB;gBACnD,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBAAE;oBAAO;oBAAK;gBAAK;YAC1C;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,KAAK,KAAK,IAAI;YAChC;YAEA,sDAAsD;YACtD,IAAI,MAAM;gBACR,MAAM;YACR;QACF,EAAE,OAAO,KAAU;YACjB,SAAS,IAAI,OAAO,IAAI;YACxB,MAAM;QACR;IACF;IAEA,MAAM,gBAAgB,OAAO;QAC3B,IAAI;YACF,SAAS;YAET,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,mIAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,qBAAqB,CAAC,OAAO;gBACjE,YAAY,GAAG,OAAO,QAAQ,CAAC,MAAM,CAAC,oBAAoB,CAAC;YAC7D;YAEA,IAAI,OAAO,MAAM;QACnB,EAAE,OAAO,KAAU;YACjB,SAAS,IAAI,OAAO,IAAI;YACxB,MAAM;QACR;IACF;IAEA,MAAM,gBAAgB,OAAO;QAC3B,IAAI;YACF,SAAS;YAET,IAAI,CAAC,MAAM,MAAM,IAAI,MAAM;YAE3B,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,mIAAA,CAAA,WAAQ,CAC7B,IAAI,CAAC,SACL,MAAM,CAAC,SACP,EAAE,CAAC,MAAM,KAAK,EAAE;YAEnB,IAAI,OAAO,MAAM;YAEjB,0BAA0B;YAC1B,QAAQ;gBAAE,GAAG,IAAI;gBAAE,GAAG,OAAO;YAAC;QAChC,EAAE,OAAO,KAAU;YACjB,SAAS,IAAI,OAAO,IAAI;YACxB,MAAM;QACR;IACF;IAEA,MAAM,cAAc;QAClB,IAAI;YACF,MAAM,EAAE,MAAM,EAAE,MAAM,YAAY,EAAE,EAAE,GAAG,MAAM,mIAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,OAAO;YACpE,IAAI,cAAc;gBAChB,MAAM,iBAAiB;YACzB;QACF,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,0BAA0B;QAC1C;IACF;IAEA,MAAM,QAAyB;QAC7B;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;IACF;IAEA,qBAAO,6LAAC,YAAY,QAAQ;QAAC,OAAO;kBAAQ;;;;;;AAC9C;GAvOgB;KAAA;AAyOT,SAAS;;IACd,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;IANgB", "debugId": null}}]}