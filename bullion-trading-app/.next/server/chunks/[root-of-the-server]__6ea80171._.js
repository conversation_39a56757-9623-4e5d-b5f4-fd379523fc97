module.exports = {

"[project]/.next-internal/server/app/api/prices/stream/route/actions.js [app-rsc] (server actions loader, ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
}}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/stream [external] (stream, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("stream", () => require("stream"));

module.exports = mod;
}}),
"[externals]/http [external] (http, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("http", () => require("http"));

module.exports = mod;
}}),
"[externals]/url [external] (url, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("url", () => require("url"));

module.exports = mod;
}}),
"[externals]/punycode [external] (punycode, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("punycode", () => require("punycode"));

module.exports = mod;
}}),
"[externals]/https [external] (https, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("https", () => require("https"));

module.exports = mod;
}}),
"[externals]/zlib [external] (zlib, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("zlib", () => require("zlib"));

module.exports = mod;
}}),
"[externals]/events [external] (events, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("events", () => require("events"));

module.exports = mod;
}}),
"[externals]/net [external] (net, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("net", () => require("net"));

module.exports = mod;
}}),
"[externals]/tls [external] (tls, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("tls", () => require("tls"));

module.exports = mod;
}}),
"[externals]/crypto [external] (crypto, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("crypto", () => require("crypto"));

module.exports = mod;
}}),
"[externals]/buffer [external] (buffer, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("buffer", () => require("buffer"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}}),
"[project]/src/lib/api/metal-rates.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "MetalRatesSSEClient": (()=>MetalRatesSSEClient),
    "MetalRatesService": (()=>MetalRatesService)
});
const API_KEY = process.env.METAL_RATES_API_KEY || 'your-api-key-here';
const API_URL = process.env.METAL_RATES_API_URL || 'https://metal-api.boogafantastic.workers.dev';
class MetalRatesService {
    /**
   * Fetch current metal rates (REST API fallback)
   */ static async getCurrentRates() {
        try {
            const response = await fetch(`${API_URL}/${API_KEY}/rates`, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json'
                }
            });
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            const data = await response.json();
            return data.rates || [];
        } catch (error) {
            console.error('Error fetching metal rates:', error);
            throw error;
        }
    }
    /**
   * Get historical data for a specific metal
   */ static async getHistoricalData(symbol, period = '24h') {
        try {
            const response = await fetch(`${API_URL}/${API_KEY}/historical/${symbol}?period=${period}`, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json'
                }
            });
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            const data = await response.json();
            return data.historical || [];
        } catch (error) {
            console.error('Error fetching historical data:', error);
            throw error;
        }
    }
}
class MetalRatesSSEClient {
    apiKey;
    eventSource = null;
    reconnectAttempts = 0;
    isConnected = false;
    options;
    // Event handlers
    onRatesUpdate = null;
    onConnectionChange = null;
    onMaxReconnectAttemptsReached = null;
    onError = null;
    constructor(apiKey = API_KEY, options = {}){
        this.apiKey = apiKey;
        this.options = {
            autoReconnect: true,
            maxReconnectAttempts: 10,
            reconnectDelay: 1000,
            ...options
        };
        this.connect();
    }
    connect() {
        const sseUrl = `${API_URL}/${this.apiKey}/stream`;
        try {
            this.eventSource = new EventSource(sseUrl);
            this.setupEventListeners();
        } catch (error) {
            console.error('[SSE] Failed to create EventSource:', error);
            this.scheduleReconnect();
        }
    }
    setupEventListeners() {
        if (!this.eventSource) return;
        this.eventSource.onopen = ()=>{
            console.log('[SSE] Connected successfully');
            this.isConnected = true;
            this.reconnectAttempts = 0;
            this.onConnectionChange?.(true);
        };
        this.eventSource.addEventListener('data', (event)=>{
            try {
                const message = JSON.parse(event.data);
                this.handleDataMessage(message);
            } catch (error) {
                console.error('[SSE] Error parsing data message:', error);
            }
        });
        this.eventSource.addEventListener('heartbeat', (event)=>{
            console.log('[SSE] Received heartbeat');
        // Heartbeat received - connection is alive
        });
        this.eventSource.onerror = (error)=>{
            console.error('[SSE] Connection error:', error);
            this.isConnected = false;
            this.onConnectionChange?.(false);
            this.onError?.(error);
            if (this.options.autoReconnect) {
                this.scheduleReconnect();
            }
        };
    }
    handleDataMessage(message) {
        switch(message.type){
            case 'initial':
                console.log(`[SSE] Received initial rates: ${message.count} items`);
                if (message.data) {
                    this.onRatesUpdate?.(message.data, 'initial');
                }
                break;
            case 'update':
                console.log(`[SSE] Received rate update: ${message.count} items`);
                if (message.data) {
                    this.onRatesUpdate?.(message.data, 'update');
                }
                break;
            default:
                console.log('[SSE] Unknown message type:', message.type);
        }
    }
    scheduleReconnect() {
        if (this.reconnectAttempts >= this.options.maxReconnectAttempts) {
            console.error('[SSE] Max reconnection attempts reached');
            this.onMaxReconnectAttemptsReached?.();
            return;
        }
        const delay = this.options.reconnectDelay * Math.pow(2, this.reconnectAttempts);
        this.reconnectAttempts++;
        console.log(`[SSE] Reconnecting in ${delay}ms (attempt ${this.reconnectAttempts})`);
        setTimeout(()=>{
            this.connect();
        }, delay);
    }
    disconnect() {
        if (this.eventSource) {
            this.eventSource.close();
            this.eventSource = null;
            this.isConnected = false;
            this.onConnectionChange?.(false);
        }
    }
    getConnectionStatus() {
        return this.isConnected;
    }
}
}}),
"[project]/src/app/api/prices/stream/route.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "GET": (()=>GET),
    "dynamic": (()=>dynamic),
    "runtime": (()=>runtime)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$supabase$2f$ssr$2f$dist$2f$module$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/@supabase/ssr/dist/module/index.js [app-route] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$supabase$2f$ssr$2f$dist$2f$module$2f$createServerClient$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@supabase/ssr/dist/module/createServerClient.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$headers$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/headers.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$metal$2d$rates$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/metal-rates.ts [app-route] (ecmascript)");
;
;
;
const runtime = 'nodejs';
const dynamic = 'force-dynamic';
async function GET(request) {
    // Verify authentication
    const cookieStore = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$headers$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["cookies"])();
    const supabase = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$supabase$2f$ssr$2f$dist$2f$module$2f$createServerClient$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["createServerClient"])(("TURBOPACK compile-time value", "https://fiygbooyjywsfhvoczgn.supabase.co"), ("TURBOPACK compile-time value", "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZpeWdib295anl3c2Zodm9jemduIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTA3ODc5MjUsImV4cCI6MjA2NjM2MzkyNX0.-7_6275H_K5puqXre6CPzfcIRGLl-xqG6hYze_XHiis"), {
        cookies: {
            get (name) {
                return cookieStore.get(name)?.value;
            },
            set (name, value, options) {
                cookieStore.set({
                    name,
                    value,
                    ...options
                });
            },
            remove (name, options) {
                cookieStore.set({
                    name,
                    value: '',
                    ...options
                });
            }
        }
    });
    const { data: { session } } = await supabase.auth.getSession();
    if (!session) {
        return new Response('Unauthorized', {
            status: 401
        });
    }
    // Set up SSE headers
    const headers = new Headers({
        'Content-Type': 'text/event-stream',
        'Cache-Control': 'no-cache, no-transform',
        'Connection': 'keep-alive',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Cache-Control'
    });
    const encoder = new TextEncoder();
    let isConnected = true;
    const stream = new ReadableStream({
        start (controller) {
            // Send initial connection message
            const initialMessage = `data: ${JSON.stringify({
                type: 'connected',
                timestamp: new Date().toISOString()
            })}\n\n`;
            controller.enqueue(encoder.encode(initialMessage));
            // Set up metal rates SSE client
            const metalRatesClient = new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$metal$2d$rates$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["MetalRatesSSEClient"]();
            // Handle rate updates from external API
            metalRatesClient.onRatesUpdate = async (rates, type)=>{
                if (!isConnected) return;
                try {
                    // Update database with new prices
                    await updatePricesInDatabase(rates, supabase);
                    // Send update to client
                    const message = `data: ${JSON.stringify({
                        type: 'price_update',
                        data: rates,
                        timestamp: new Date().toISOString()
                    })}\n\n`;
                    controller.enqueue(encoder.encode(message));
                } catch (error) {
                    console.error('Error processing price update:', error);
                }
            };
            // Handle connection status changes
            metalRatesClient.onConnectionChange = (connected)=>{
                if (!isConnected) return;
                const message = `data: ${JSON.stringify({
                    type: 'connection_status',
                    connected,
                    timestamp: new Date().toISOString()
                })}\n\n`;
                controller.enqueue(encoder.encode(message));
            };
            // Send heartbeat every 30 seconds
            const heartbeatInterval = setInterval(()=>{
                if (!isConnected) {
                    clearInterval(heartbeatInterval);
                    return;
                }
                const heartbeat = `data: ${JSON.stringify({
                    type: 'heartbeat',
                    timestamp: new Date().toISOString()
                })}\n\n`;
                controller.enqueue(encoder.encode(heartbeat));
            }, 30000);
            // Send initial prices
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$metal$2d$rates$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["MetalRatesService"].getCurrentRates().then((rates)=>{
                if (!isConnected) return;
                const message = `data: ${JSON.stringify({
                    type: 'initial_prices',
                    data: rates,
                    timestamp: new Date().toISOString()
                })}\n\n`;
                controller.enqueue(encoder.encode(message));
            }).catch((error)=>{
                console.error('Error fetching initial prices:', error);
            });
            // Cleanup on close
            request.signal.addEventListener('abort', ()=>{
                isConnected = false;
                metalRatesClient.disconnect();
                clearInterval(heartbeatInterval);
                controller.close();
            });
        },
        cancel () {
            isConnected = false;
        }
    });
    return new Response(stream, {
        headers
    });
}
async function updatePricesInDatabase(rates, supabase) {
    try {
        for (const rate of rates){
            // Update current price in bullion_products
            const { error: updateError } = await supabase.from('bullion_products').update({
                current_price: rate.price,
                updated_at: new Date().toISOString()
            }).eq('symbol', rate.symbol.toUpperCase());
            if (updateError) {
                console.error('Error updating product price:', updateError);
                continue;
            }
            // Insert price history record
            const { error: historyError } = await supabase.from('price_history').insert({
                product_id: await getProductIdBySymbol(rate.symbol, supabase),
                price: rate.price,
                currency: rate.currency || 'INR',
                source: 'metal_api',
                timestamp: new Date().toISOString()
            });
            if (historyError) {
                console.error('Error inserting price history:', historyError);
            }
        }
    } catch (error) {
        console.error('Error updating prices in database:', error);
        throw error;
    }
}
async function getProductIdBySymbol(symbol, supabase) {
    try {
        const { data, error } = await supabase.from('bullion_products').select('id').eq('symbol', symbol.toUpperCase()).single();
        if (error || !data) {
            console.error('Error getting product ID:', error);
            return null;
        }
        return data.id;
    } catch (error) {
        console.error('Error in getProductIdBySymbol:', error);
        return null;
    }
}
}}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__6ea80171._.js.map