'use client'

import { ProtectedRoute } from '@/components/auth/ProtectedRoute'
import { useAuth } from '@/contexts/AuthContext'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { PriceDashboard, CompactPriceDashboard } from '@/components/prices/PriceDashboard'
import { LogOut, User, Phone, Mail, Shield } from 'lucide-react'

export default function DashboardPage() {
  return (
    <ProtectedRoute>
      <DashboardContent />
    </ProtectedRoute>
  )
}

function DashboardContent() {
  const { user, signOut } = useAuth()

  const handleSignOut = async () => {
    try {
      await signOut()
    } catch (error) {
      console.error('Sign out error:', error)
    }
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <h1 className="text-2xl font-bold text-amber-600">BullionTrade</h1>
              </div>
            </div>
            <div className="flex items-center space-x-4">
              <span className="text-sm text-gray-700">
                Welcome, {user?.full_name}
              </span>
              <Button
                variant="outline"
                size="sm"
                onClick={handleSignOut}
                className="flex items-center space-x-2"
              >
                <LogOut className="h-4 w-4" />
                <span>Sign Out</span>
              </Button>
            </div>
          </div>
        </div>
      </header>

      {/* Price Ticker */}
      <div className="bg-white border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-3">
          <CompactPriceDashboard />
        </div>
      </div>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          {/* Live Prices Section */}
          <div className="mb-8">
            <PriceDashboard />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            
            {/* User Profile Card */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <User className="h-5 w-5" />
                  <span>Profile Information</span>
                </CardTitle>
                <CardDescription>Your account details</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center space-x-2">
                  <Mail className="h-4 w-4 text-gray-500" />
                  <span className="text-sm">{user?.email}</span>
                  {user?.is_verified ? (
                    <Badge variant="secondary" className="text-xs">Verified</Badge>
                  ) : (
                    <Badge variant="destructive" className="text-xs">Unverified</Badge>
                  )}
                </div>
                
                {user?.phone && (
                  <div className="flex items-center space-x-2">
                    <Phone className="h-4 w-4 text-gray-500" />
                    <span className="text-sm">{user.phone}</span>
                    {user.is_phone_verified ? (
                      <Badge variant="secondary" className="text-xs">Verified</Badge>
                    ) : (
                      <Badge variant="destructive" className="text-xs">Unverified</Badge>
                    )}
                  </div>
                )}

                <div className="flex items-center space-x-2">
                  <Shield className="h-4 w-4 text-gray-500" />
                  <span className="text-sm">
                    2FA: {user?.two_factor_enabled ? 'Enabled' : 'Disabled'}
                  </span>
                  {user?.two_factor_enabled ? (
                    <Badge variant="secondary" className="text-xs">Secure</Badge>
                  ) : (
                    <Badge variant="outline" className="text-xs">Setup Recommended</Badge>
                  )}
                </div>

                <div className="pt-2">
                  <Badge 
                    variant={user?.status === 'active' ? 'default' : 'destructive'}
                    className="text-xs"
                  >
                    Status: {user?.status}
                  </Badge>
                </div>
              </CardContent>
            </Card>

            {/* Portfolio Summary Card */}
            <Card>
              <CardHeader>
                <CardTitle>Portfolio Summary</CardTitle>
                <CardDescription>Your bullion holdings</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-center py-8">
                  <p className="text-gray-500">No holdings yet</p>
                  <Button className="mt-4" size="sm">
                    Start Trading
                  </Button>
                </div>
              </CardContent>
            </Card>

            {/* Quick Actions Card */}
            <Card>
              <CardHeader>
                <CardTitle>Quick Actions</CardTitle>
                <CardDescription>Common tasks</CardDescription>
              </CardHeader>
              <CardContent className="space-y-2">
                <Button variant="outline" className="w-full justify-start">
                  View Market Prices
                </Button>
                <Button variant="outline" className="w-full justify-start">
                  Place Order
                </Button>
                <Button variant="outline" className="w-full justify-start">
                  Set Price Alert
                </Button>
                <Button variant="outline" className="w-full justify-start">
                  View Transaction History
                </Button>
              </CardContent>
            </Card>

          </div>

          {/* Welcome Message */}
          <Card className="mt-6">
            <CardHeader>
              <CardTitle>Welcome to BullionTrade</CardTitle>
              <CardDescription>
                Your secure platform for precious metals trading
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="prose max-w-none">
                <p className="text-gray-600">
                  You've successfully logged into your BullionTrade account. Here you can:
                </p>
                <ul className="list-disc list-inside text-gray-600 mt-2 space-y-1">
                  <li>View real-time precious metals prices</li>
                  <li>Buy and sell gold, silver, platinum, and palladium</li>
                  <li>Track your portfolio performance</li>
                  <li>Set price alerts for your favorite metals</li>
                  <li>Manage your account settings and security</li>
                </ul>
                
                {!user?.is_verified && (
                  <div className="mt-4 p-4 bg-yellow-50 border border-yellow-200 rounded-md">
                    <p className="text-yellow-800 text-sm">
                      <strong>Action Required:</strong> Please verify your email address to unlock all features.
                    </p>
                  </div>
                )}

                {user?.phone && !user?.is_phone_verified && (
                  <div className="mt-4 p-4 bg-blue-50 border border-blue-200 rounded-md">
                    <p className="text-blue-800 text-sm">
                      <strong>Recommended:</strong> Verify your phone number for enhanced security and notifications.
                    </p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </div>
      </main>
    </div>
  )
}
