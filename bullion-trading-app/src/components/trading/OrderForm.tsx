'use client'

import { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Product } from '@/components/products/ProductCard'
import { cn } from '@/lib/utils'
import {
  ShoppingCart,
  TrendingDown,
  Calculator,
  AlertCircle,
  CheckCircle,
  Info,
  DollarSign,
  Weight,
  Percent,
  Clock
} from 'lucide-react'

export interface OrderData {
  product_id: string
  order_type: 'buy' | 'sell'
  quantity: number
  price_per_unit: number
  total_amount: number
  order_method: 'market' | 'limit'
  limit_price?: number
}

interface OrderFormProps {
  product?: Product
  orderType: 'buy' | 'sell'
  onSubmit?: (orderData: OrderData) => void
  onCancel?: () => void
  className?: string
}

export function OrderForm({
  product,
  orderType,
  onSubmit,
  onCancel,
  className
}: OrderFormProps) {
  const [quantity, setQuantity] = useState(1)
  const [orderMethod, setOrderMethod] = useState<'market' | 'limit'>('market')
  const [limitPrice, setLimitPrice] = useState(product?.current_price || 0)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [errors, setErrors] = useState<Record<string, string>>({})

  const currentPrice = product?.current_price || 0
  const totalAmount = quantity * (orderMethod === 'limit' ? limitPrice : currentPrice)
  const estimatedFees = totalAmount * 0.01 // 1% fee
  const finalAmount = totalAmount + estimatedFees

  useEffect(() => {
    if (product && orderMethod === 'limit') {
      setLimitPrice(product.current_price)
    }
  }, [product, orderMethod])

  const validateForm = () => {
    const newErrors: Record<string, string> = {}

    if (!product) {
      newErrors.product = 'Please select a product'
    }

    if (quantity <= 0) {
      newErrors.quantity = 'Quantity must be greater than 0'
    }

    if (product?.stock_quantity && quantity > product.stock_quantity) {
      newErrors.quantity = 'Quantity exceeds available stock'
    }

    if (orderMethod === 'limit' && limitPrice <= 0) {
      newErrors.limitPrice = 'Limit price must be greater than 0'
    }

    if (orderType === 'sell' && !product) {
      newErrors.product = 'Please select a product to sell'
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!validateForm() || !product) return

    setIsSubmitting(true)

    try {
      const orderData: OrderData = {
        product_id: product.id,
        order_type: orderType,
        quantity,
        price_per_unit: orderMethod === 'limit' ? limitPrice : currentPrice,
        total_amount: totalAmount,
        order_method: orderMethod,
        limit_price: orderMethod === 'limit' ? limitPrice : undefined
      }

      await onSubmit?.(orderData)
    } catch (error) {
      console.error('Order submission error:', error)
    } finally {
      setIsSubmitting(false)
    }
  }

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(price)
  }

  const formatWeight = (weight: number, unit: string) => {
    return `${weight}${unit}`
  }

  return (
    <Card className={cn('w-full max-w-2xl', className)}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              {orderType === 'buy' ? (
                <ShoppingCart className="h-5 w-5 text-green-600" />
              ) : (
                <TrendingDown className="h-5 w-5 text-red-600" />
              )}
              {orderType === 'buy' ? 'Buy Order' : 'Sell Order'}
            </CardTitle>
            <CardDescription>
              {orderType === 'buy' 
                ? 'Purchase precious metals at current market rates'
                : 'Sell your precious metals holdings'
              }
            </CardDescription>
          </div>
          <Badge 
            variant={orderType === 'buy' ? 'default' : 'destructive'}
            className="text-sm"
          >
            {orderType.toUpperCase()}
          </Badge>
        </div>
      </CardHeader>

      <CardContent className="space-y-6">
        {/* Product Information */}
        {product && (
          <div className="p-4 bg-gray-50 rounded-lg">
            <div className="flex items-center justify-between mb-2">
              <h3 className="font-medium text-gray-900">{product.name}</h3>
              <Badge variant="secondary" className={
                product.metal_type === 'gold' ? 'bg-yellow-100 text-yellow-800' :
                product.metal_type === 'silver' ? 'bg-gray-100 text-gray-800' :
                product.metal_type === 'platinum' ? 'bg-blue-100 text-blue-800' :
                'bg-purple-100 text-purple-800'
              }>
                {product.metal_type}
              </Badge>
            </div>
            <div className="grid grid-cols-3 gap-4 text-sm text-gray-600">
              <div>
                <span className="block font-medium">Weight</span>
                <span>{formatWeight(product.weight, product.weight_unit)}</span>
              </div>
              <div>
                <span className="block font-medium">Purity</span>
                <span>{product.purity}%</span>
              </div>
              <div>
                <span className="block font-medium">Current Price</span>
                <span className="text-lg font-bold text-gray-900">
                  {formatPrice(product.current_price)}
                </span>
              </div>
            </div>
          </div>
        )}

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Order Method */}
          <div className="space-y-3">
            <label className="block text-sm font-medium text-gray-700">
              Order Type
            </label>
            <div className="flex gap-4">
              <label className="flex items-center">
                <input
                  type="radio"
                  value="market"
                  checked={orderMethod === 'market'}
                  onChange={(e) => setOrderMethod(e.target.value as 'market' | 'limit')}
                  className="mr-2"
                />
                <span className="text-sm">Market Order</span>
              </label>
              <label className="flex items-center">
                <input
                  type="radio"
                  value="limit"
                  checked={orderMethod === 'limit'}
                  onChange={(e) => setOrderMethod(e.target.value as 'market' | 'limit')}
                  className="mr-2"
                />
                <span className="text-sm">Limit Order</span>
              </label>
            </div>
            <p className="text-xs text-gray-500">
              {orderMethod === 'market' 
                ? 'Execute immediately at current market price'
                : 'Execute only when price reaches your specified limit'
              }
            </p>
          </div>

          {/* Quantity */}
          <div className="space-y-2">
            <label className="block text-sm font-medium text-gray-700">
              Quantity
            </label>
            <div className="relative">
              <Weight className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                type="number"
                value={quantity}
                onChange={(e) => setQuantity(Math.max(1, parseInt(e.target.value) || 1))}
                min="1"
                max={product?.stock_quantity}
                className="pl-10"
                placeholder="Enter quantity"
              />
            </div>
            {errors.quantity && (
              <p className="text-sm text-red-600">{errors.quantity}</p>
            )}
            {product?.stock_quantity && (
              <p className="text-xs text-gray-500">
                Available: {product.stock_quantity} units
              </p>
            )}
          </div>

          {/* Limit Price (if limit order) */}
          {orderMethod === 'limit' && (
            <div className="space-y-2">
              <label className="block text-sm font-medium text-gray-700">
                Limit Price (per unit)
              </label>
              <div className="relative">
                <DollarSign className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  type="number"
                  value={limitPrice}
                  onChange={(e) => setLimitPrice(parseFloat(e.target.value) || 0)}
                  min="0"
                  step="0.01"
                  className="pl-10"
                  placeholder="Enter limit price"
                />
              </div>
              {errors.limitPrice && (
                <p className="text-sm text-red-600">{errors.limitPrice}</p>
              )}
              {product && (
                <p className="text-xs text-gray-500">
                  Current market price: {formatPrice(product.current_price)}
                </p>
              )}
            </div>
          )}

          {/* Order Summary */}
          <div className="p-4 bg-gray-50 rounded-lg space-y-3">
            <h3 className="font-medium text-gray-900 flex items-center gap-2">
              <Calculator className="h-4 w-4" />
              Order Summary
            </h3>
            
            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span className="text-gray-600">Quantity:</span>
                <span className="font-medium">{quantity} units</span>
              </div>
              
              <div className="flex justify-between">
                <span className="text-gray-600">Price per unit:</span>
                <span className="font-medium">
                  {formatPrice(orderMethod === 'limit' ? limitPrice : currentPrice)}
                </span>
              </div>
              
              <div className="flex justify-between">
                <span className="text-gray-600">Subtotal:</span>
                <span className="font-medium">{formatPrice(totalAmount)}</span>
              </div>
              
              <div className="flex justify-between">
                <span className="text-gray-600">Estimated fees (1%):</span>
                <span className="font-medium">{formatPrice(estimatedFees)}</span>
              </div>
              
              <div className="flex justify-between pt-2 border-t border-gray-200">
                <span className="font-medium text-gray-900">Total:</span>
                <span className="font-bold text-lg text-gray-900">
                  {formatPrice(finalAmount)}
                </span>
              </div>
            </div>
          </div>

          {/* Warnings/Info */}
          {orderMethod === 'limit' && (
            <Alert>
              <Clock className="h-4 w-4" />
              <AlertDescription>
                Limit orders may not execute immediately and will remain active until filled or cancelled.
              </AlertDescription>
            </Alert>
          )}

          {orderType === 'sell' && (
            <Alert>
              <Info className="h-4 w-4" />
              <AlertDescription>
                Selling precious metals may have tax implications. Please consult with a tax advisor.
              </AlertDescription>
            </Alert>
          )}

          {/* Action Buttons */}
          <div className="flex gap-3 pt-4">
            <Button
              type="submit"
              disabled={isSubmitting || !product}
              className={cn(
                'flex-1',
                orderType === 'buy' 
                  ? 'bg-green-600 hover:bg-green-700' 
                  : 'bg-red-600 hover:bg-red-700'
              )}
            >
              {isSubmitting ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                  Processing...
                </>
              ) : (
                <>
                  {orderType === 'buy' ? (
                    <ShoppingCart className="h-4 w-4 mr-2" />
                  ) : (
                    <TrendingDown className="h-4 w-4 mr-2" />
                  )}
                  {orderType === 'buy' ? 'Place Buy Order' : 'Place Sell Order'}
                </>
              )}
            </Button>
            
            {onCancel && (
              <Button
                type="button"
                variant="outline"
                onClick={onCancel}
                disabled={isSubmitting}
              >
                Cancel
              </Button>
            )}
          </div>
        </form>

        {/* Risk Disclaimer */}
        <div className="p-3 bg-amber-50 border border-amber-200 rounded-lg">
          <div className="flex items-start gap-2">
            <AlertCircle className="h-4 w-4 text-amber-600 mt-0.5 flex-shrink-0" />
            <div className="text-xs text-amber-800">
              <p className="font-medium mb-1">Risk Disclaimer:</p>
              <p>
                Precious metals trading involves risk. Prices can fluctuate significantly. 
                Only invest what you can afford to lose. Past performance does not guarantee future results.
              </p>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
