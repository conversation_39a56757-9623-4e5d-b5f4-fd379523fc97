'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs'
import { cn } from '@/lib/utils'
import {
  Clock,
  CheckCircle,
  XCircle,
  AlertCircle,
  TrendingUp,
  TrendingDown,
  Calendar,
  DollarSign,
  Weight,
  MoreHorizontal,
  Eye,
  X,
  RefreshCw
} from 'lucide-react'

export interface Order {
  id: string
  product_name: string
  product_id: string
  metal_type: 'gold' | 'silver' | 'platinum' | 'palladium'
  order_type: 'buy' | 'sell'
  order_method: 'market' | 'limit'
  quantity: number
  price_per_unit: number
  limit_price?: number
  total_amount: number
  status: 'pending' | 'filled' | 'cancelled' | 'partially_filled'
  created_at: string
  updated_at: string
  filled_quantity?: number
  filled_price?: number
  fees?: number
}

interface OrderManagementProps {
  orders: Order[]
  onCancelOrder?: (orderId: string) => void
  onViewOrder?: (orderId: string) => void
  onRefresh?: () => void
  className?: string
}

export function OrderManagement({
  orders,
  onCancelOrder,
  onViewOrder,
  onRefresh,
  className
}: OrderManagementProps) {
  const [selectedTab, setSelectedTab] = useState('active')

  const activeOrders = orders.filter(order => 
    order.status === 'pending' || order.status === 'partially_filled'
  )
  
  const completedOrders = orders.filter(order => 
    order.status === 'filled' || order.status === 'cancelled'
  )

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(price)
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-IN', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const getStatusColor = (status: Order['status']) => {
    switch (status) {
      case 'pending':
        return 'bg-yellow-100 text-yellow-800'
      case 'filled':
        return 'bg-green-100 text-green-800'
      case 'cancelled':
        return 'bg-red-100 text-red-800'
      case 'partially_filled':
        return 'bg-blue-100 text-blue-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const getStatusIcon = (status: Order['status']) => {
    switch (status) {
      case 'pending':
        return <Clock className="h-4 w-4" />
      case 'filled':
        return <CheckCircle className="h-4 w-4" />
      case 'cancelled':
        return <XCircle className="h-4 w-4" />
      case 'partially_filled':
        return <AlertCircle className="h-4 w-4" />
      default:
        return <Clock className="h-4 w-4" />
    }
  }

  const getMetalColor = (metal: string) => {
    switch (metal) {
      case 'gold': return 'bg-yellow-100 text-yellow-800'
      case 'silver': return 'bg-gray-100 text-gray-800'
      case 'platinum': return 'bg-blue-100 text-blue-800'
      case 'palladium': return 'bg-purple-100 text-purple-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const OrderCard = ({ order }: { order: Order }) => (
    <Card className="hover:shadow-md transition-shadow duration-200">
      <CardContent className="p-6">
        <div className="flex items-start justify-between mb-4">
          <div className="flex-1">
            <div className="flex items-center gap-2 mb-2">
              <h3 className="font-medium text-gray-900">{order.product_name}</h3>
              <Badge variant="secondary" className={getMetalColor(order.metal_type)}>
                {order.metal_type}
              </Badge>
            </div>
            
            <div className="flex items-center gap-4 text-sm text-gray-600">
              <div className="flex items-center gap-1">
                {order.order_type === 'buy' ? (
                  <TrendingUp className="h-4 w-4 text-green-600" />
                ) : (
                  <TrendingDown className="h-4 w-4 text-red-600" />
                )}
                <span className={order.order_type === 'buy' ? 'text-green-600' : 'text-red-600'}>
                  {order.order_type.toUpperCase()}
                </span>
              </div>
              
              <div className="flex items-center gap-1">
                <Calendar className="h-4 w-4" />
                <span>{formatDate(order.created_at)}</span>
              </div>
            </div>
          </div>

          <div className="flex items-center gap-2">
            <Badge className={getStatusColor(order.status)}>
              {getStatusIcon(order.status)}
              <span className="ml-1">{order.status.replace('_', ' ')}</span>
            </Badge>
            
            <div className="flex items-center gap-1">
              {onViewOrder && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => onViewOrder(order.id)}
                  className="p-1"
                >
                  <Eye className="h-4 w-4" />
                </Button>
              )}
              
              {(order.status === 'pending' || order.status === 'partially_filled') && onCancelOrder && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => onCancelOrder(order.id)}
                  className="p-1 text-red-600 hover:text-red-700"
                >
                  <X className="h-4 w-4" />
                </Button>
              )}
            </div>
          </div>
        </div>

        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
          <div>
            <span className="block text-gray-500 mb-1">Quantity</span>
            <span className="font-medium">
              {order.filled_quantity && order.status === 'partially_filled' 
                ? `${order.filled_quantity}/${order.quantity}`
                : order.quantity
              } units
            </span>
          </div>
          
          <div>
            <span className="block text-gray-500 mb-1">Price</span>
            <span className="font-medium">
              {order.order_method === 'limit' && order.limit_price
                ? formatPrice(order.limit_price)
                : formatPrice(order.price_per_unit)
              }
            </span>
            {order.order_method === 'limit' && (
              <Badge variant="outline" className="text-xs ml-1">LIMIT</Badge>
            )}
          </div>
          
          <div>
            <span className="block text-gray-500 mb-1">Total Amount</span>
            <span className="font-medium">{formatPrice(order.total_amount)}</span>
          </div>
          
          <div>
            <span className="block text-gray-500 mb-1">
              {order.status === 'filled' ? 'Filled Price' : 'Status'}
            </span>
            <span className="font-medium">
              {order.status === 'filled' && order.filled_price
                ? formatPrice(order.filled_price)
                : order.status.replace('_', ' ')
              }
            </span>
          </div>
        </div>

        {order.fees && order.status === 'filled' && (
          <div className="mt-4 pt-4 border-t border-gray-100">
            <div className="flex justify-between text-sm">
              <span className="text-gray-500">Fees:</span>
              <span className="font-medium">{formatPrice(order.fees)}</span>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  )

  const OrderTable = ({ orders }: { orders: Order[] }) => (
    <div className="overflow-x-auto">
      <table className="w-full">
        <thead>
          <tr className="border-b border-gray-200">
            <th className="text-left py-3 px-4 font-medium text-gray-900">Product</th>
            <th className="text-left py-3 px-4 font-medium text-gray-900">Type</th>
            <th className="text-left py-3 px-4 font-medium text-gray-900">Quantity</th>
            <th className="text-left py-3 px-4 font-medium text-gray-900">Price</th>
            <th className="text-left py-3 px-4 font-medium text-gray-900">Total</th>
            <th className="text-left py-3 px-4 font-medium text-gray-900">Status</th>
            <th className="text-left py-3 px-4 font-medium text-gray-900">Date</th>
            <th className="text-left py-3 px-4 font-medium text-gray-900">Actions</th>
          </tr>
        </thead>
        <tbody>
          {orders.map((order) => (
            <tr key={order.id} className="border-b border-gray-100 hover:bg-gray-50">
              <td className="py-3 px-4">
                <div>
                  <div className="font-medium text-gray-900">{order.product_name}</div>
                  <Badge variant="secondary" className={cn('text-xs', getMetalColor(order.metal_type))}>
                    {order.metal_type}
                  </Badge>
                </div>
              </td>
              <td className="py-3 px-4">
                <div className="flex items-center gap-1">
                  {order.order_type === 'buy' ? (
                    <TrendingUp className="h-4 w-4 text-green-600" />
                  ) : (
                    <TrendingDown className="h-4 w-4 text-red-600" />
                  )}
                  <span className={cn(
                    'text-sm font-medium',
                    order.order_type === 'buy' ? 'text-green-600' : 'text-red-600'
                  )}>
                    {order.order_type.toUpperCase()}
                  </span>
                </div>
              </td>
              <td className="py-3 px-4">
                <span className="text-sm">
                  {order.filled_quantity && order.status === 'partially_filled' 
                    ? `${order.filled_quantity}/${order.quantity}`
                    : order.quantity
                  }
                </span>
              </td>
              <td className="py-3 px-4">
                <div className="text-sm">
                  {order.order_method === 'limit' && order.limit_price
                    ? formatPrice(order.limit_price)
                    : formatPrice(order.price_per_unit)
                  }
                  {order.order_method === 'limit' && (
                    <Badge variant="outline" className="text-xs ml-1">LIMIT</Badge>
                  )}
                </div>
              </td>
              <td className="py-3 px-4">
                <span className="text-sm font-medium">{formatPrice(order.total_amount)}</span>
              </td>
              <td className="py-3 px-4">
                <Badge className={getStatusColor(order.status)}>
                  {getStatusIcon(order.status)}
                  <span className="ml-1">{order.status.replace('_', ' ')}</span>
                </Badge>
              </td>
              <td className="py-3 px-4">
                <span className="text-sm text-gray-600">{formatDate(order.created_at)}</span>
              </td>
              <td className="py-3 px-4">
                <div className="flex items-center gap-1">
                  {onViewOrder && (
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => onViewOrder(order.id)}
                      className="p-1"
                    >
                      <Eye className="h-4 w-4" />
                    </Button>
                  )}
                  
                  {(order.status === 'pending' || order.status === 'partially_filled') && onCancelOrder && (
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => onCancelOrder(order.id)}
                      className="p-1 text-red-600 hover:text-red-700"
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  )}
                </div>
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  )

  return (
    <div className={cn('space-y-6', className)}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Order Management</h2>
          <p className="text-gray-600">Track and manage your trading orders</p>
        </div>
        
        {onRefresh && (
          <Button variant="outline" onClick={onRefresh}>
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
        )}
      </div>

      {/* Order Tabs */}
      <Tabs value={selectedTab} onValueChange={setSelectedTab}>
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="active" className="flex items-center gap-2">
            <Clock className="h-4 w-4" />
            Active Orders ({activeOrders.length})
          </TabsTrigger>
          <TabsTrigger value="history" className="flex items-center gap-2">
            <CheckCircle className="h-4 w-4" />
            Order History ({completedOrders.length})
          </TabsTrigger>
        </TabsList>

        <TabsContent value="active" className="space-y-4">
          {activeOrders.length === 0 ? (
            <Card>
              <CardContent className="flex flex-col items-center justify-center py-12">
                <Clock className="h-12 w-12 text-gray-400 mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  No Active Orders
                </h3>
                <p className="text-gray-600 text-center">
                  You don't have any pending or partially filled orders at the moment.
                </p>
              </CardContent>
            </Card>
          ) : (
            <div className="space-y-4">
              {/* Card View for Mobile */}
              <div className="block md:hidden space-y-4">
                {activeOrders.map((order) => (
                  <OrderCard key={order.id} order={order} />
                ))}
              </div>
              
              {/* Table View for Desktop */}
              <div className="hidden md:block">
                <Card>
                  <CardContent className="p-0">
                    <OrderTable orders={activeOrders} />
                  </CardContent>
                </Card>
              </div>
            </div>
          )}
        </TabsContent>

        <TabsContent value="history" className="space-y-4">
          {completedOrders.length === 0 ? (
            <Card>
              <CardContent className="flex flex-col items-center justify-center py-12">
                <CheckCircle className="h-12 w-12 text-gray-400 mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  No Order History
                </h3>
                <p className="text-gray-600 text-center">
                  Your completed and cancelled orders will appear here.
                </p>
              </CardContent>
            </Card>
          ) : (
            <div className="space-y-4">
              {/* Card View for Mobile */}
              <div className="block md:hidden space-y-4">
                {completedOrders.map((order) => (
                  <OrderCard key={order.id} order={order} />
                ))}
              </div>
              
              {/* Table View for Desktop */}
              <div className="hidden md:block">
                <Card>
                  <CardContent className="p-0">
                    <OrderTable orders={completedOrders} />
                  </CardContent>
                </Card>
              </div>
            </div>
          )}
        </TabsContent>
      </Tabs>
    </div>
  )
}
