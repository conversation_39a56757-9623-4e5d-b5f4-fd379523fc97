'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs'
import { cn } from '@/lib/utils'
import {
  TrendingUp,
  TrendingDown,
  DollarSign,
  Wallet,
  BarChart3,
  PieChart,
  Activity,
  Calendar,
  Award,
  Target,
  RefreshCw,
  Download,
  Eye,
  EyeOff
} from 'lucide-react'

export interface Holding {
  id: string
  product_name: string
  metal_type: 'gold' | 'silver' | 'platinum' | 'palladium'
  quantity: number
  weight_unit: 'g' | 'oz' | 'kg'
  average_buy_price: number
  current_price: number
  total_value: number
  unrealized_pnl: number
  unrealized_pnl_percent: number
  purchase_date: string
  last_updated: string
}

export interface PortfolioStats {
  total_value: number
  total_invested: number
  total_pnl: number
  total_pnl_percent: number
  day_change: number
  day_change_percent: number
  holdings_count: number
  diversification: {
    gold: number
    silver: number
    platinum: number
    palladium: number
  }
}

interface PortfolioOverviewProps {
  stats: PortfolioStats
  holdings: Holding[]
  onRefresh?: () => void
  onExport?: () => void
  className?: string
}

// Mock data for demonstration
const mockStats: PortfolioStats = {
  total_value: 2850000,
  total_invested: 2650000,
  total_pnl: 200000,
  total_pnl_percent: 7.55,
  day_change: 15000,
  day_change_percent: 0.53,
  holdings_count: 8,
  diversification: {
    gold: 65,
    silver: 20,
    platinum: 10,
    palladium: 5
  }
}

const mockHoldings: Holding[] = [
  {
    id: '1',
    product_name: 'Gold Bar 10g',
    metal_type: 'gold',
    quantity: 5,
    weight_unit: 'g',
    average_buy_price: 64000,
    current_price: 65500,
    total_value: 327500,
    unrealized_pnl: 7500,
    unrealized_pnl_percent: 2.34,
    purchase_date: '2024-01-15',
    last_updated: new Date().toISOString()
  },
  {
    id: '2',
    product_name: 'Silver Coin 1oz',
    metal_type: 'silver',
    quantity: 20,
    weight_unit: 'oz',
    average_buy_price: 2400,
    current_price: 2550,
    total_value: 51000,
    unrealized_pnl: 3000,
    unrealized_pnl_percent: 6.25,
    purchase_date: '2024-02-10',
    last_updated: new Date().toISOString()
  }
]

export function PortfolioOverview({
  stats = mockStats,
  holdings = mockHoldings,
  onRefresh,
  onExport,
  className
}: PortfolioOverviewProps) {
  const [showValues, setShowValues] = useState(true)
  const [selectedTimeframe, setSelectedTimeframe] = useState('1M')

  const formatPrice = (price: number) => {
    if (!showValues) return '••••••'
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(price)
  }

  const formatPercent = (percent: number) => {
    return `${percent > 0 ? '+' : ''}${percent.toFixed(2)}%`
  }

  const getMetalColor = (metal: string) => {
    switch (metal) {
      case 'gold': return 'bg-yellow-100 text-yellow-800'
      case 'silver': return 'bg-gray-100 text-gray-800'
      case 'platinum': return 'bg-blue-100 text-blue-800'
      case 'palladium': return 'bg-purple-100 text-purple-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const StatCard = ({ 
    title, 
    value, 
    change, 
    changePercent, 
    icon: Icon, 
    trend,
    description 
  }: {
    title: string
    value: string | number
    change?: number
    changePercent?: number
    icon: React.ComponentType<{ className?: string }>
    trend?: 'up' | 'down' | 'neutral'
    description?: string
  }) => (
    <Card>
      <CardContent className="p-6">
        <div className="flex items-center justify-between mb-2">
          <p className="text-sm font-medium text-gray-600">{title}</p>
          <div className="p-2 bg-amber-50 rounded-full">
            <Icon className="h-4 w-4 text-amber-600" />
          </div>
        </div>
        
        <div className="space-y-1">
          <p className="text-2xl font-bold text-gray-900">{value}</p>
          
          {change !== undefined && changePercent !== undefined && (
            <div className={cn(
              'flex items-center text-sm',
              trend === 'up' ? 'text-green-600' : 
              trend === 'down' ? 'text-red-600' : 'text-gray-600'
            )}>
              {trend === 'up' && <TrendingUp className="h-3 w-3 mr-1" />}
              {trend === 'down' && <TrendingDown className="h-3 w-3 mr-1" />}
              <span>
                {formatPrice(change)} ({formatPercent(changePercent)})
              </span>
            </div>
          )}
          
          {description && (
            <p className="text-xs text-gray-500">{description}</p>
          )}
        </div>
      </CardContent>
    </Card>
  )

  return (
    <div className={cn('space-y-6', className)}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Portfolio Overview</h1>
          <p className="text-gray-600">Track your precious metals investments</p>
        </div>
        
        <div className="flex items-center gap-3">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setShowValues(!showValues)}
          >
            {showValues ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
          </Button>
          
          {onExport && (
            <Button variant="outline" size="sm" onClick={onExport}>
              <Download className="h-4 w-4 mr-2" />
              Export
            </Button>
          )}
          
          {onRefresh && (
            <Button variant="outline" size="sm" onClick={onRefresh}>
              <RefreshCw className="h-4 w-4 mr-2" />
              Refresh
            </Button>
          )}
        </div>
      </div>

      {/* Portfolio Stats */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <StatCard
          title="Total Portfolio Value"
          value={formatPrice(stats.total_value)}
          change={stats.day_change}
          changePercent={stats.day_change_percent}
          icon={Wallet}
          trend={stats.day_change >= 0 ? 'up' : 'down'}
          description="Today's change"
        />
        
        <StatCard
          title="Total Invested"
          value={formatPrice(stats.total_invested)}
          icon={DollarSign}
          description="Cost basis"
        />
        
        <StatCard
          title="Unrealized P&L"
          value={formatPrice(stats.total_pnl)}
          changePercent={stats.total_pnl_percent}
          icon={TrendingUp}
          trend={stats.total_pnl >= 0 ? 'up' : 'down'}
          description="Since inception"
        />
        
        <StatCard
          title="Holdings"
          value={stats.holdings_count}
          icon={Award}
          description="Active positions"
        />
      </div>

      {/* Diversification Chart */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <Card className="lg:col-span-2">
          <CardHeader>
            <CardTitle>Portfolio Allocation</CardTitle>
            <CardDescription>Distribution by metal type</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {Object.entries(stats.diversification).map(([metal, percentage]) => (
                <div key={metal} className="space-y-2">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <Badge variant="secondary" className={getMetalColor(metal)}>
                        {metal}
                      </Badge>
                      <span className="text-sm font-medium capitalize">{metal}</span>
                    </div>
                    <span className="text-sm font-medium">{percentage}%</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div
                      className={cn(
                        'h-2 rounded-full',
                        metal === 'gold' ? 'bg-yellow-500' :
                        metal === 'silver' ? 'bg-gray-500' :
                        metal === 'platinum' ? 'bg-blue-500' :
                        'bg-purple-500'
                      )}
                      style={{ width: `${percentage}%` }}
                    />
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Performance Summary</CardTitle>
            <CardDescription>Key metrics</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">Best Performer</span>
              <div className="text-right">
                <p className="text-sm font-medium">Silver</p>
                <p className="text-xs text-green-600">+6.25%</p>
              </div>
            </div>
            
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">Total Return</span>
              <div className="text-right">
                <p className="text-sm font-medium">{formatPercent(stats.total_pnl_percent)}</p>
                <p className="text-xs text-gray-500">All time</p>
              </div>
            </div>
            
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">Holdings Count</span>
              <div className="text-right">
                <p className="text-sm font-medium">{stats.holdings_count}</p>
                <p className="text-xs text-gray-500">Active positions</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Holdings Table */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>Holdings</CardTitle>
              <CardDescription>Your precious metals positions</CardDescription>
            </div>
            
            <div className="flex items-center gap-2">
              {['1D', '1W', '1M', '3M', '1Y'].map((timeframe) => (
                <Button
                  key={timeframe}
                  variant={selectedTimeframe === timeframe ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setSelectedTimeframe(timeframe)}
                >
                  {timeframe}
                </Button>
              ))}
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b border-gray-200">
                  <th className="text-left py-3 px-4 font-medium text-gray-900">Asset</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-900">Quantity</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-900">Avg. Price</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-900">Current Price</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-900">Market Value</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-900">P&L</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-900">P&L %</th>
                </tr>
              </thead>
              <tbody>
                {holdings.map((holding) => (
                  <tr key={holding.id} className="border-b border-gray-100 hover:bg-gray-50">
                    <td className="py-3 px-4">
                      <div className="flex items-center gap-2">
                        <div>
                          <p className="font-medium text-gray-900">{holding.product_name}</p>
                          <Badge variant="secondary" className={cn('text-xs', getMetalColor(holding.metal_type))}>
                            {holding.metal_type}
                          </Badge>
                        </div>
                      </div>
                    </td>
                    <td className="py-3 px-4">
                      <span className="text-sm">{holding.quantity} {holding.weight_unit}</span>
                    </td>
                    <td className="py-3 px-4">
                      <span className="text-sm">{formatPrice(holding.average_buy_price)}</span>
                    </td>
                    <td className="py-3 px-4">
                      <span className="text-sm">{formatPrice(holding.current_price)}</span>
                    </td>
                    <td className="py-3 px-4">
                      <span className="text-sm font-medium">{formatPrice(holding.total_value)}</span>
                    </td>
                    <td className="py-3 px-4">
                      <span className={cn(
                        'text-sm font-medium',
                        holding.unrealized_pnl >= 0 ? 'text-green-600' : 'text-red-600'
                      )}>
                        {holding.unrealized_pnl >= 0 ? '+' : ''}{formatPrice(holding.unrealized_pnl)}
                      </span>
                    </td>
                    <td className="py-3 px-4">
                      <div className="flex items-center gap-1">
                        {holding.unrealized_pnl_percent >= 0 ? (
                          <TrendingUp className="h-3 w-3 text-green-600" />
                        ) : (
                          <TrendingDown className="h-3 w-3 text-red-600" />
                        )}
                        <span className={cn(
                          'text-sm font-medium',
                          holding.unrealized_pnl_percent >= 0 ? 'text-green-600' : 'text-red-600'
                        )}>
                          {formatPercent(holding.unrealized_pnl_percent)}
                        </span>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
